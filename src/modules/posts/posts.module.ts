import { <PERSON>du<PERSON> } from '@nestjs/common';

import { PostsService } from './posts.service';
import { PostsController } from './posts.controller';

import { TagsModule } from '@/modules/tags/tags.module';
import { WorkspacesModule } from '@/modules/workspaces/workspaces.module';
import { UsersModule } from '@/modules/users/users.module';
import { PostTagsModule } from './post-tags/post-tags.module';
import { PostMediasModule } from './media/post-medias.module';
import { ArticlePostsModule } from './article-posts/article-posts.module';
import { QuestionPostsModule } from './question-posts/question-posts.module';
import { PostLikesModule } from './post-likes/post-likes.module';
import { PostCommentsModule } from './post-comments/post-comments.module';

@Module({
  imports: [
    TagsModule,
    PostTagsModule,
    WorkspacesModule,
    ArticlePostsModule,
    PostMediasModule,
    QuestionPostsModule,
    UsersModule,
    PostLikesModule,
    PostCommentsModule,
  ],
  controllers: [PostsController],
  providers: [PostsService],
  exports: [PostsService],
})
export class PostsModule {}

import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, exists, gte, ilike, lte, ne, or, sql } from 'drizzle-orm';

import * as schema from '@/db/schema';
import {
  posts,
  NewPost,
  tags,
  postTags,
  workspaces,
  users,
  postLikes,
  postMedias,
  postComments,
} from '@/db/schema';

import { countRelation, isLikedByEntity } from '@/utils/database';

import { EntityName } from '@/constants/entities';
import { PostTagsStatus } from '@/constants/post-tags';
import { PrimarySpeciality } from '@/constants/workspaces';
import { PostActiveStatus, PostStatus, PostType, PostTypeJoinFields } from '@/constants/posts';
import { PostLikesStatus } from '@/constants/post-likes';
import { PostCommentsStatus } from '@/constants/post-comments';
import { PostMediaStatus } from '@/constants/post-media';

import { audiencePermissionError } from '@/exceptions/posts';
import { itemNotFound } from '@/exceptions/common';

@Injectable()
export class PostsService {
  constructor(@Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>) {}

  async findAll({
    limit,
    offset,
    entityId,
    searchKeyword,
    postType,
  }: {
    limit: number;
    offset: number;
    entityId: string;
    searchKeyword?: string;
    postType?: PostType;
  }) {
    // Normalize the keyword by converting to lowercase and removing all spaces
    const normalizedKeyword = searchKeyword?.toLowerCase().replace(/\s+/g, '');

    // Create condition to match posts by tag name or publisher display name
    const keywordFilter = normalizedKeyword
      ? or(
          // Match tags
          exists(
            this.db
              .select()
              .from(postTags)
              .where(
                and(
                  eq(postTags.postId, posts.id),
                  eq(postTags.status, PostTagsStatus.ACTIVE),
                  exists(
                    this.db
                      .select()
                      .from(tags)
                      .where(
                        and(
                          eq(tags.id, postTags.tagId),
                          ilike(tags.name, `%${normalizedKeyword}%`),
                        ),
                      ),
                  ),
                ),
              ),
          ),
          // Match publisher's display name (without spaces)
          exists(
            this.db
              .select()
              .from(workspaces)
              .where(
                and(
                  eq(workspaces.id, posts.workspaceId),
                  exists(
                    this.db
                      .select()
                      .from(users)
                      .where(
                        and(
                          eq(users.id, workspaces.createdById),
                          sql`LOWER(REPLACE(${users.displayName}, ' ', '')) LIKE ${`%${normalizedKeyword}%`}`,
                        ),
                      ),
                  ),
                ),
              ),
          ),
          // Match post content using full-text search
          sql`${posts.contentTsv} @@ plainto_tsquery('english', ${normalizedKeyword})`,
        )
      : undefined;

    const postTypeFilter = postType ? eq(posts.postType, postType) : undefined;

    const rawPosts = await this.db.query.posts.findMany({
      columns: {
        id: true,
        postType: true,
        tags: true,
        content: true,
      },
      where: and(
        lte(posts.postScheduleDate, new Date()),
        eq(posts.status, PostActiveStatus.ACTIVE),
        ne(posts.postStatus, PostStatus.DRAFT),
        keywordFilter,
        postTypeFilter,
      ),
      orderBy: (post, { desc }) => [desc(post.postScheduleDate)],
      with: {
        // questionPost: {
        //   columns: {},
        //   // TODO: update here once we have option to pin comment
        // },
        articlePost: {
          columns: {
            title: true,
            coverImagePath: true,
          },
        },
        workspace: {
          columns: {},
          with: {
            createdByUser: {
              columns: {
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: (media, { asc }) => [asc(media.order)],
        },
      },
      extras: (fields) => ({
        postedAt: sql`${posts.postScheduleDate}`.as('posted_at'),
        ...isLikedByEntity('isLiked', fields.id, entityId),
        ...countRelation(
          'commentsCount',
          fields.id,
          postComments.postId,
          PostCommentsStatus.ACTIVE,
        ),
        ...countRelation('likesCount', fields.id, postLikes.postId, PostLikesStatus.ACTIVE),
        // TODO: update once the SHARE, & REPOST tracking is added
        shareCount: sql<number>`0`.as('share_count'),
        repostCount: sql<number>`0`.as('repost_count'),
      }),
      limit,
      offset,
    });

    // Transform each post for frontend response
    return rawPosts.map((dbPost) => {
      type Post = typeof dbPost;

      // Clone the post while making workspace optional for safe deletion
      const post: Omit<Post, 'workspace'> & Partial<Pick<Post, 'workspace'>> = dbPost;

      // Extract publisher info from workspace relation
      const { displayName: publisherName, ...publisherDetails } = dbPost.workspace.createdByUser;

      delete post.workspace;

      // Extract only the relevant post-type data (articlePost, questionPost, etc.)
      let flattenedPostTypeData: Record<string, any> = {};

      for (const [postTypeKey, fieldKey] of Object.entries(PostTypeJoinFields)) {
        const typedFieldKey = fieldKey as keyof Post;
        if (post.postType === postTypeKey) {
          flattenedPostTypeData = post[typedFieldKey] as Record<string, any>;
          delete post[typedFieldKey];
        } else {
          delete post[typedFieldKey];
        }
      }

      return {
        ...post,
        ...flattenedPostTypeData,
        ...publisherDetails,
        publisherName,
      };
    });
  }

  async findAllByTag({
    tagName,
    limit,
    offset,
    entityId,
    searchKeyword,
    postType,
  }: {
    tagName: string;
    limit: number;
    offset: number;
    entityId: string;
    searchKeyword?: string;
    postType?: PostType;
  }) {
    // Normalize the keyword by converting to lowercase and removing all spaces
    const normalizedKeyword = searchKeyword?.toLowerCase().replace(/\s+/g, '');

    // For tag endpoint, searchKeyword matches against publisher names and post content (not tags)
    const keywordFilter = normalizedKeyword
      ? or(
          // Match publisher's display name (without spaces)
          exists(
            this.db
              .select()
              .from(workspaces)
              .where(
                and(
                  eq(workspaces.id, posts.workspaceId),
                  exists(
                    this.db
                      .select()
                      .from(users)
                      .where(
                        and(
                          eq(users.id, workspaces.createdById),
                          sql`LOWER(REPLACE(${users.displayName}, ' ', '')) LIKE ${`%${normalizedKeyword}%`}`,
                        ),
                      ),
                  ),
                ),
              ),
          ),
          // Match post content using full-text search
          sql`${posts.contentTsv} @@ plainto_tsquery('english', ${normalizedKeyword})`,
        )
      : undefined;

    const postTypeFilter = postType ? eq(posts.postType, postType) : undefined;

    // Filter posts by the specific tag
    const tagFilter = exists(
      this.db
        .select()
        .from(postTags)
        .where(
          and(
            eq(postTags.postId, posts.id),
            eq(postTags.status, PostTagsStatus.ACTIVE),
            exists(
              this.db
                .select()
                .from(tags)
                .where(
                  and(
                    eq(tags.id, postTags.tagId),
                    sql`LOWER(${tags.name}) = ${tagName.toLowerCase()}`,
                  ),
                ),
            ),
          ),
        ),
    );

    const rawPosts = await this.db.query.posts.findMany({
      columns: {
        id: true,
        postType: true,
        tags: true,
        content: true,
      },
      where: and(
        lte(posts.postScheduleDate, new Date()),
        eq(posts.status, PostActiveStatus.ACTIVE),
        ne(posts.postStatus, PostStatus.DRAFT),
        tagFilter,
        keywordFilter,
        postTypeFilter,
      ),
      orderBy: (post, { desc }) => [desc(post.postScheduleDate)],
      with: {
        articlePost: {
          columns: {
            title: true,
            coverImagePath: true,
          },
        },
        workspace: {
          columns: {},
          with: {
            createdByUser: {
              columns: {
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: (media, { asc }) => [asc(media.order)],
        },
      },
      extras: (fields) => ({
        postedAt: sql`${posts.postScheduleDate}`.as('posted_at'),
        ...isLikedByEntity('isLiked', fields.id, entityId),
        ...countRelation(
          'commentsCount',
          fields.id,
          postComments.postId,
          PostCommentsStatus.ACTIVE,
        ),
        ...countRelation('likesCount', fields.id, postLikes.postId, PostLikesStatus.ACTIVE),
        // TODO: update once the SHARE, & REPOST tracking is added
        shareCount: sql<number>`0`.as('share_count'),
        repostCount: sql<number>`0`.as('repost_count'),
      }),
      limit,
      offset,
    });

    // Transform each post for frontend response
    const transformedPosts = rawPosts.map((dbPost) => {
      type Post = typeof dbPost;

      // Clone the post while making workspace optional for safe deletion
      const post: Omit<Post, 'workspace'> & Partial<Pick<Post, 'workspace'>> = dbPost;

      // Extract publisher info from workspace relation
      const { displayName: publisherName, ...publisherDetails } = dbPost.workspace.createdByUser;

      delete post.workspace;

      // Extract only the relevant post-type data (textPost, mediaPost, etc.)
      let flattenedPostTypeData: Record<string, any> = {};

      for (const [postTypeKey, fieldKey] of Object.entries(PostTypeJoinFields)) {
        const typedFieldKey = fieldKey as keyof Post;
        if (post.postType === postTypeKey) {
          flattenedPostTypeData = post[typedFieldKey] as Record<string, any>;
          delete post[typedFieldKey];
        } else {
          delete post[typedFieldKey];
        }
      }

      return {
        ...post,
        ...flattenedPostTypeData,
        ...publisherDetails,
        publisherName,
      };
    });

    // Return response with tag information
    return {
      posts: transformedPosts,
      // TODO: Implement tag following functionality
      isFollowed: false, // Placeholder for tag following status, Update once following functionality is added
    };
  }

  async createPost(data: NewPost, tx?: PostgresJsDatabase<typeof schema>) {
    const queryRunner = tx || this.db;
    const [post] = await queryRunner.insert(posts).values(data).returning();
    return post;
  }

  async updatePost(
    postId: string,
    updateData: Partial<NewPost>,
    updatedById: string,
    tx?: PostgresJsDatabase<typeof schema>,
  ) {
    const queryRunner = tx || this.db;

    // Verify post exists
    const existingPost = await this.getPostById(postId);

    if (!existingPost) {
      throw itemNotFound(EntityName.POST);
    }

    const [updatedPost] = await queryRunner
      .update(posts)
      .set({
        ...updateData,
        updatedById,
      })
      .where(eq(posts.id, postId))
      .returning();

    return updatedPost;
  }

  // Get a post by ID
  async getPostById(postId: string) {
    const post = await this.db.query.posts.findFirst({
      where: eq(posts.id, postId),
      with: {
        publishedBy: true,
        updatedBy: true,
        postTags: {
          with: {
            tag: true,
          },
        },
      },
    });

    if (!post) {
      throw itemNotFound(EntityName.POST);
    }

    return post;
  }

  // CHECK THE PERMISSION TO CREATE POST FOR AUDIENCE WHICH IS PASSED IN PARAMETE
  async checkAudiencePermission(
    workpsacePrimarySpeciality: PrimarySpeciality,
    audience: PrimarySpeciality,
  ) {
    // If workspace has BOTH speciality, they can choose any audience
    if (workpsacePrimarySpeciality === PrimarySpeciality.BOTH) {
      return; // Allow any audience selection
    }

    // If workspace speciality doesn't match the audience (and isn't BOTH)
    if (workpsacePrimarySpeciality !== audience) {
      throw audiencePermissionError();
    }
  }

  // Get all draft posts for a workspace
  async getDraftPosts(workspaceId: string) {
    const postsData = await this.db.query.posts.findMany({
      columns: {
        id: true,
        postType: true,
        postScheduleDate: true,
        content: true,
      },
      where: and(
        eq(posts.workspaceId, workspaceId),
        eq(posts.postStatus, PostStatus.DRAFT),
        eq(posts.status, PostActiveStatus.ACTIVE),
      ),
      with: {
        articlePost: {
          columns: {
            title: true,
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          limit: 1,
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: (media, { asc }) => [asc(media.order)],
          extras: {
            totalMediasCount:
              sql<number>`(select count(*) from ${schema.postMedias} where ${schema.postMedias.postId} = ${schema.posts.id})`.as(
                'total_medias_count',
              ),
          },
        },
      },
      orderBy: (post, { asc }) => [asc(post.postScheduleDate)],
    });

    return postsData.map((postData) => {
      const post: any = { ...postData };

      // Only flatten articlePost if present
      if (post.postType === PostType.ARTICLE && post.articlePost) {
        Object.assign(post, post.articlePost);
        delete post.articlePost;
      }

      return post;
    });
  }

  // Get all scheduled posts for a workspace
  async getScheduledPosts(workspaceId: string) {
    const now = new Date();

    const postsData = await this.db.query.posts.findMany({
      columns: {
        id: true,
        postType: true,
        postScheduleDate: true,
        content: true,
      },
      where: and(
        eq(posts.workspaceId, workspaceId),
        eq(posts.postStatus, PostStatus.SCHEDULED),
        eq(posts.status, PostActiveStatus.ACTIVE),
        gte(posts.postScheduleDate, now), // Only get posts scheduled for the future
      ),
      with: {
        articlePost: {
          columns: {
            title: true,
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          limit: 1,
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: (media, { asc }) => [asc(media.order)],
          extras: {
            totalMediasCount:
              sql<number>`(select count(*) from ${schema.postMedias} where ${schema.postMedias.postId} = ${schema.posts.id})`.as(
                'total_medias_count',
              ),
          },
        },
      },
      orderBy: (post, { asc }) => [asc(post.postScheduleDate)],
    });

    return postsData.map((postData) => {
      const post: any = { ...postData };

      // Only flatten articlePost if present
      if (post.postType === PostType.ARTICLE && post.articlePost) {
        Object.assign(post, post.articlePost);
        delete post.articlePost;
      }

      return post;
    });
  }
}

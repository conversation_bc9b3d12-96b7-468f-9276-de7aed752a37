import { ApiProperty, OmitType } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEnum, IsN<PERSON>ber, IsOptional, IsString, Max, Min } from 'class-validator';

import { PostType } from '@/constants/posts';

export class FeedFetchPostDto {
  @ApiProperty({
    description: 'Search query, and this will match against the tags, publisher name and content',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase().replace(/\s+/g, ''))
  searchKeyword?: string;

  @ApiProperty({
    description: 'Filter posts by post type',
    required: false,
  })
  @IsEnum(PostType)
  @IsOptional()
  postType?: PostType;

  @ApiProperty({
    description: 'Maximum number of results to return, default is 25',
    example: 25,
    required: false,
    default: 25,
  })
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @Min(1)
  @Max(50)
  limit: number = 25;

  @ApiProperty({
    description: 'Number of results to skip (offset), used for pagination/infinite scroll',
    example: 0,
    required: false,
    default: 0,
  })
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @Min(0)
  offset: number = 0;
}

export class TagFeedFetchPostDto extends OmitType(FeedFetchPostDto, ['searchKeyword']) {
  @ApiProperty({
    description: 'Search query, and this will match against publisher name, content',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase().replace(/\s+/g, ''))
  searchKeyword?: string;
}

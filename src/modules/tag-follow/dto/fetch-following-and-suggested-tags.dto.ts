import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

export class FetchFollowingAndSuggestedTagsDto {
  @ApiProperty({
    description: 'Search query, and this will match against the tags',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase().replace(/\s+/g, ''))
  searchKeyword?: string;
}

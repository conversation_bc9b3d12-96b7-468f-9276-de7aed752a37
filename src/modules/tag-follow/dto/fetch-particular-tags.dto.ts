import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsInt, IsOptional, IsString, Max, Min } from 'class-validator';

export class FetchParticularTagsDto {
  @ApiProperty({
    description: 'Search query, and this will match against the tags',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase().replace(/\s+/g, ''))
  searchKeyword?: string;

  @ApiProperty({
    description: 'The number of tags to skip.',
    required: false,
    default: 0,
  })
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @IsOptional()
  offset: number = 0;

  @ApiProperty({
    description: 'The number of tags to return. defaults to 10',
    required: false,
    default: 10,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(50)
  @IsOptional()
  limit: number = 10;
}

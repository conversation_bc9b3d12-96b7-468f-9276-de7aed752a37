import { Inject, Injectable } from '@nestjs/common';
// import { CreateUserFollowerDto } from './dto/create-user-follower.dto';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, lte, sql, inArray, notInArray, desc } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { tagFollowers, tags, postTags, posts, postMedias } from '@/db/schema';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';
import { EntityType } from '@/constants/user-types';
import { SUGGESTED_TAGS_LIMIT, TagFollowerStatus } from '@/constants/tag-follow';
import { PostActiveStatus, PostType } from '@/constants/posts';
import { TagStatus } from '@/constants/tags';
import { PostTagsStatus } from '@/constants/post-tags';
import { PostMediaStatus } from '@/constants/post-media';

// ------------------------------------------------------------

type PostRow = {
  tagId: string;
  tagName: string;
  postId: string;
  postType: PostType.MEDIA | PostType.TEXT;
  postScheduleDate: Date;
  content?: string | null;
  mediaCount?: number | null;
  mediaItems?: {
    mediaPath: string;
    mediaType: string;
    altText: string | null;
  }[];
};

type FormattedPost = {
  postId: string;
  postType: PostType.MEDIA | PostType.TEXT;
  content?: string | null;
  mediaCount?: number | null;
  postMedias?: {
    mediaPath: string;
    mediaType: string;
    altText: string | null;
  }[];
};

type GroupedTagPost = {
  tagId: string;
  tagName: string;
  posts: FormattedPost[];
};

@Injectable()
export class TagFollowService {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    // private readonly permissionsService: PermissionsService,
  ) {}

  async createFollower(entityId: string, entityType: EntityType, tagId: string) {
    const tagFollower = await this.drizzleDev.query.tagFollowers.findFirst({
      where: and(eq(tagFollowers.entityId, entityId), eq(tagFollowers.tagId, tagId)),
    });

    if (tagFollower) {
      if (tagFollower.status === TagFollowerStatus.ACTIVE) {
        throw itemAlreadyExists(EntityName.TAG_FOLLOWER);
      } else if (tagFollower.status === TagFollowerStatus.INACTIVE) {
        const [updatedTagFollower] = await this.drizzleDev
          .update(tagFollowers)
          .set({ status: TagFollowerStatus.ACTIVE })
          .where(and(eq(tagFollowers.entityId, entityId), eq(tagFollowers.tagId, tagId)))
          .returning();

        return updatedTagFollower;
      }
    }

    const [newTagFollower] = await this.drizzleDev
      .insert(tagFollowers)
      .values({
        tagId,
        entityId,
        entityType,
        status: TagFollowerStatus.ACTIVE,
      })
      .returning();

    return newTagFollower;
  }

  async softDeleteTagFollower(entityId: string, tagId: string) {
    const [deletedTagFollower] = await this.drizzleDev
      .update(tagFollowers)
      .set({ status: TagFollowerStatus.INACTIVE })
      .where(
        and(
          eq(tagFollowers.entityId, entityId),
          eq(tagFollowers.tagId, tagId),
          eq(tagFollowers.status, TagFollowerStatus.ACTIVE),
        ),
      )
      .returning();

    if (!deletedTagFollower) throw itemNotFound(EntityName.TAG_FOLLOWER);

    return deletedTagFollower;
  }

  async findLatestPostsForFollowingTag(
    entityId: string,
    searchQuery?: string,
    offset: number = 0,
    limit: number = 2,
  ) {
    const db = this.drizzleDev;

    // CTE: followed_tags
    const followedTagsCte = db
      .$with('followed_tags')
      .as(
        db
          .select({ tag_id: tagFollowers.tagId })
          .from(tagFollowers)
          .where(eq(tagFollowers.entityId, entityId)),
      );

    console.log('🔍followed tags cte');

    // CTE: tagged_posts
    const taggedPostsCte = db.$with('tagged_posts').as(
      db
        .select({
          tag_id: postTags.tagId,
          post_id: posts.id,
          content: posts.content,
          post_type: posts.postType,
          created_at: posts.createdAt,
          media_count: sql<number>`count(${postMedias.mediaPath})`
            .mapWith(Number)
            .as('media_count'),
        })
        .from(postTags)
        .innerJoin(
          posts,
          and(
            eq(postTags.postId, posts.id),
            eq(posts.status, 1),
            sql`${posts.postType} IN ('media', 'text')`,
          ),
        )
        .leftJoin(postMedias, eq(postMedias.postId, posts.id))
        .where(sql`${postTags.tagId} IN (select tagId from followed_tags)`)
        .groupBy(postTags.tagId, posts.id),
    );
    console.log('🔍 tagged posts');

    // CTE: ranked_posts
    const rankedPostsCte = db.$with('ranked_posts').as(
      db
        .select({
          tag_id: taggedPostsCte.tag_id,
          post_id: taggedPostsCte.post_id,
          content: taggedPostsCte.content,
          post_type: taggedPostsCte.post_type,
          created_at: taggedPostsCte.created_at,
          media_count: taggedPostsCte.media_count,
          postRank: sql<number>`
      ROW_NUMBER() OVER (
        PARTITION BY ${taggedPostsCte.tag_id}
        ORDER BY 
          CASE 
            WHEN ${taggedPostsCte.post_type} = 'media' AND ${taggedPostsCte.media_count} >= 3 AND ${taggedPostsCte.content} IS NOT NULL THEN 1
            WHEN ${taggedPostsCte.post_type} = 'media' AND ${taggedPostsCte.media_count} >= 2 AND ${taggedPostsCte.content} IS NOT NULL THEN 2
            WHEN ${taggedPostsCte.post_type} = 'media' AND ${taggedPostsCte.media_count} >= 1 AND ${taggedPostsCte.content} IS NOT NULL THEN 3
            WHEN ${taggedPostsCte.post_type} = 'text' AND ${taggedPostsCte.media_count} IS NOT NULL THEN 4
            ELSE 5
          END,
          ${taggedPostsCte.created_at} DESC
      )
    `
            .mapWith(Number)
            .as('post_rank'),
        })
        .from(taggedPostsCte),
    );
    console.log('🔍 ranked posts');

    // CTE: top_posts_per_tag
    const topPostsPerTagCte = db.$with('top_posts_per_tag').as(
      db
        .select()
        .from(rankedPostsCte)
        .where(sql`${rankedPostsCte.postRank} <= 2`),
    );
    console.log('🔍 top posts per tag');

    // CTE: final_result
    const finalResultCte = db.$with('final_result').as(
      db
        .select({
          tag_id: tags.id,
          tag_name: tags.name,
          status: tags.status,
          post_id: topPostsPerTagCte.post_id,
          content: topPostsPerTagCte.content,
          post_type: topPostsPerTagCte.post_type,
          created_at: topPostsPerTagCte.created_at,
          medias: sql`
      json_agg(
        json_build_object(
          'path', ${postMedias.mediaPath},
          'type', ${postMedias.mediaType},
          'alt', ${postMedias.altText}
        )
      ) FILTER (WHERE ${postMedias.mediaPath} IS NOT NULL)
    `.as('medias'),
        })
        .from(tags)
        .innerJoin(followedTagsCte, eq(followedTagsCte.tag_id, tags.id))
        .innerJoin(topPostsPerTagCte, eq(topPostsPerTagCte.tag_id, tags.id))
        .leftJoin(postMedias, eq(postMedias.postId, topPostsPerTagCte.post_id))
        .where(
          and(
            eq(tags.status, 1),
            searchQuery ? sql`${tags.name} ILIKE '%' || ${searchQuery} || '%'` : sql`TRUE`,
          ),
        )
        .groupBy(
          tags.id,
          tags.name,
          tags.status,
          topPostsPerTagCte.post_id,
          topPostsPerTagCte.content,
          topPostsPerTagCte.post_type,
          topPostsPerTagCte.created_at,
        ),
    );

    console.log('🔍 final result');

    // Final SELECT
    const result = await db
      .with(followedTagsCte, taggedPostsCte, rankedPostsCte, topPostsPerTagCte, finalResultCte)
      .select()
      .from(finalResultCte)
      .orderBy(desc(finalResultCte.created_at))
      .limit(limit)
      .offset(offset);

    console.log('⚡️ result', result);

    const groupedMap = new Map();

    for (const row of result) {
      const tagId = row.tag_id;

      if (!groupedMap.has(tagId)) {
        groupedMap.set(tagId, {
          tagId: row.tag_id,
          tagName: row.tag_name,
          posts: [],
        });
      }

      groupedMap.get(tagId).posts.push({
        postId: row.post_id,
        content: row.content,
        postType: row.post_type,
        createdAt: row.created_at,
        medias: row.medias || [], // already parsed JSON array from query
      });
    }

    return Array.from(groupedMap.values());
  }

  async findSuggestedTagsWithPosts(entityId: string) {
    const db = this.drizzleDev;

    const followed = await db
      .select({ tagId: tagFollowers.tagId })
      .from(tagFollowers)
      .where(
        and(eq(tagFollowers.entityId, entityId), eq(tagFollowers.status, TagFollowerStatus.ACTIVE)),
      );

    const followedIds = followed.map((f) => f.tagId);

    const suggestedTags = await db
      .select({ tagId: tags.id, tagName: tags.name })
      .from(tags)
      .where(
        and(
          eq(tags.status, TagStatus.ACTIVE),
          followedIds.length > 0 ? notInArray(tags.id, followedIds) : sql`TRUE`,
        ),
      )
      .orderBy(sql`RANDOM()`)
      .limit(SUGGESTED_TAGS_LIMIT);

    if (suggestedTags.length === 0) return [];

    const postsData = await this.getTopPostsForTags(suggestedTags.map((t) => t.tagId));
    // Only return tags that have at least one post
    const tagsWithPosts = suggestedTags.filter((tag) =>
      postsData.some((post) => post.tagId === tag.tagId),
    );
    return this.groupAndScorePosts(postsData, tagsWithPosts);
  }

  private groupAndScorePosts(
    postsData: PostRow[],
    tagsDetails: { tagId: string; tagName: string }[],
  ): GroupedTagPost[] {
    return tagsDetails.map((tag) => {
      const relevantPosts = postsData
        .filter((p) => p.tagId === tag.tagId)
        .map((p) => {
          const hasCaption = (p.content?.trim()?.length || 0) > 0;
          let priority = 0;

          if (p.postType === PostType.MEDIA) {
            if (p.mediaCount! >= 3 && hasCaption) priority = 1;
            else if (p.mediaCount === 2 && hasCaption) priority = 2;
            else if (p.mediaCount === 1 && hasCaption) priority = 3;
          } else if (p.postType === PostType.TEXT && hasCaption) {
            priority = 4;
          }

          return { ...p, priority };
        })
        .filter((p) => p.priority > 0)
        .sort(
          (a, b) =>
            a.priority - b.priority ||
            b.postScheduleDate.toISOString().localeCompare(a.postScheduleDate.toISOString()),
        )
        .slice(0, 2)
        .map((p) => ({
          postId: p.postId,
          postType: p.postType,
          content: p.content,
          mediaCount: p.mediaCount,
          postMedias: p.mediaItems,
        }));

      return {
        tagId: tag.tagId,
        tagName: tag.tagName,
        posts: relevantPosts,
      };
    });
  }

  private async getTopPostsForTags(tagIds: string[]): Promise<PostRow[]> {
    const db = this.drizzleDev;

    const data = await db
      .select({
        tagId: postTags.tagId,
        tagName: tags.name,
        postId: posts.id,
        postType: posts.postType,
        postScheduleDate: posts.postScheduleDate,
        content: posts.content,
        mediaCount: sql<number>`count(distinct ${postMedias.id})`.mapWith(Number),
        mediaItems: sql<
          {
            mediaPath: string;
            mediaType: string;
            altText: string | null;
          }[]
        >`
        json_agg(
          jsonb_build_object(
            'mediaPath', ${postMedias.mediaPath},
            'mediaType', ${postMedias.mediaType},
            'altText', ${postMedias.altText}
          )
          ORDER BY ${postMedias.order} ASC
        )
      `.mapWith((v) => v ?? []),
      })
      .from(postTags)
      .innerJoin(tags, and(eq(postTags.tagId, tags.id), eq(tags.status, TagStatus.ACTIVE)))
      .innerJoin(
        posts,
        and(
          eq(postTags.postId, posts.id),
          eq(posts.status, PostActiveStatus.ACTIVE),
          inArray(posts.postType, [PostType.MEDIA, PostType.TEXT]),
          lte(posts.postScheduleDate, new Date()),
        ),
      )
      .leftJoin(
        postMedias,
        and(eq(posts.id, postMedias.postId), eq(postMedias.status, PostMediaStatus.ACTIVE)),
      )
      .where(and(inArray(postTags.tagId, tagIds), eq(postTags.status, PostTagsStatus.ACTIVE)))
      .groupBy(
        postTags.tagId,
        tags.name,
        posts.id,
        posts.postType,
        posts.postScheduleDate,
        posts.content,
      );

    return data.map((d) => ({
      ...d,
      postType: d.postType === PostType.MEDIA ? PostType.MEDIA : PostType.TEXT,
    }));
  }
  // async findFollowStatsOfAWorkspaceById(workspaceId: string, entityId?: string) {
  //   const [{ followingCount }] = await this.drizzleDev
  //     .select({ followingCount: count() })
  //     .from(workspaceFollowers)
  //     .where(
  //       and(
  //         eq(workspaceFollowers.entityId, workspaceId),
  //         eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //       ),
  //     );

  //   const [{ followersCount }] = await this.drizzleDev
  //     .select({ followersCount: count() })
  //     .from(workspaceFollowers)
  //     .where(
  //       and(
  //         eq(workspaceFollowers.workspaceId, workspaceId),
  //         eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //       ),
  //     );

  //   const response: { followersCount: number; followingCount: number; isFollowing?: boolean } = {
  //     followersCount,
  //     followingCount,
  //   };

  //   if (entityId) {
  //     const isExist = await this.drizzleDev.query.workspaceFollowers.findFirst({
  //       where: and(
  //         eq(workspaceFollowers.entityId, entityId),
  //         eq(workspaceFollowers.workspaceId, workspaceId),
  //         eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //       ),
  //     });

  //     if (isExist) response.isFollowing = true;
  //   }

  //   return response;
  // }

  // async findAllFollowingForId(entityId: string) {
  //   return this.drizzleDev.query.workspaceFollowers.findMany({
  //     where: and(
  //       eq(workspaceFollowers.entityId, entityId),
  //       eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //     ),
  //     columns: {},
  //     // eslint-disable-next-line @typescript-eslint/no-shadow
  //     orderBy: (workspaceFollowers, { desc }) => [desc(workspaceFollowers.createdAt)],
  //     with: {
  //       workspace: {
  //         columns: {
  //           id: true,
  //           type: true,
  //           workspacename: true,
  //           label: true,
  //         },
  //       },
  //     },
  //   });
  // }

  // findAllFollowersForId(workspaceId: string) {
  //   return this.drizzleDev.query.workspaceFollowers.findMany({
  //     where: and(
  //       eq(workspaceFollowers.workspaceId, workspaceId),
  //       eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //     ),
  //     columns: {},
  //     // eslint-disable-next-line @typescript-eslint/no-shadow
  //     orderBy: (workspaceFollowers, { desc }) => [desc(workspaceFollowers.createdAt)],
  //     with: {
  //       workspace: {
  //         columns: {
  //           id: true,
  //           type: true,
  //           workspacename: true,
  //           label: true,
  //         },
  //       },
  //     },
  //   });
  // }

  // async isUserOrWorkspaceFollowingParticularWorkspaceById(entityId: string, workspaceId: string) {
  //   const workspaceFollowerQuery = await this.drizzleDev.query.workspaceFollowers.findFirst({
  //     where: and(
  //       eq(workspaceFollowers.entityId, entityId),
  //       eq(workspaceFollowers.workspaceId, workspaceId),
  //       eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //     ),
  //   });

  //   if (workspaceFollowerQuery) return true;

  //   return false;
  // }
}

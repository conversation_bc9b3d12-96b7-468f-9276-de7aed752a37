import type { Knex } from 'knex';

// export async function up(knex: Knex): Promise<void> {
//   // Add the generated tsvector column for full-text search
//   await knex.raw(
//     `ALTER TABLE posts ADD COLUMN content_tsv tsvector GENERATED ALWAYS AS (to_tsvector('english', content)) STORED`,
//   );
//   // Create a GIN index on the tsvector column
//   await knex.raw(`CREATE INDEX idx_posts_content_tsv ON posts USING GIN (content_tsv)`);
// }

// export async function down(knex: Knex): Promise<void> {
//   await knex.raw(`DROP INDEX IF EXISTS idx_posts_content_tsv`);
//   await knex.raw(`ALTER TABLE posts DROP COLUMN IF EXISTS content_tsv`);
// }

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('posts', (table) => {
    table.specificType(
      'content_tsv',
      "tsvector GENERATED ALWAYS AS (to_tsvector('simple', content)) STORED",
    );
  });

  await knex.schema.raw(`
    CREATE INDEX index_posts_on_content_tsv ON posts USING GIN (content_tsv)
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.raw(`
    DROP INDEX IF EXISTS index_posts_on_content_tsv
  `);

  await knex.schema.alterTable('posts', (table) => {
    table.dropColumn('content_tsv');
  });
}
